package typex

import (
	"fmt"
	"github.com/shopspring/decimal"
	"strconv"
)

// RoundCeil 进位
func RoundCeil(value decimal.Decimal, places int32) (decimal.Decimal, error) {
	truncateValue := value.Truncate(places)
	if value.IsPositive() {
		formatStr := "0.%0" + strconv.Itoa(int(places)) + "d"
		addValue, err := decimal.NewFromString(fmt.Sprintf(formatStr, 1))
		if err != nil {
			return decimal.Zero, err
		}

		if value.Sub(truncateValue).IsPositive() {
			truncateValue = truncateValue.Add(addValue)
		}
	}

	return truncateValue, nil
}

func ConvertStringToDeciaml(_param string) decimal.Decimal {
	if _param == "" {
		_param = "0"
	}
	deParam, _ := decimal.NewFromString(_param)
	return deParam
}

func InterfaceToString(i interface{}) string {
	switch t := i.(type) {
	case string:
		return t
	}
	return ""
}

func InterfaceToInt(i interface{}) int {
	if data, ok := i.(string); ok {
		returnNum, err := strconv.Atoi(data)
		if err == nil {
			return returnNum
		}
	}
	if data, ok := i.(int); ok {
		return data
	}
	return int(InterfaceToFloat64(i))
}

func InterfaceToFloat64(_param interface{}) float64 {
	if data, ok := _param.(float64); ok {
		return data
	}
	return -1
}

func InterfaceToInt64(_param interface{}) int64 {
	if data, ok := _param.(string); ok {
		returnNum, err := strconv.ParseInt(data, 10, 64)
		if err == nil {
			return returnNum
		}
	}
	if dada, ok := _param.(int64); ok {
		return dada
	}
	return int64(InterfaceToFloat64(_param))
}

func InterfaceToDecimal(i interface{}) decimal.Decimal {
	switch t := i.(type) {
	case decimal.Decimal:
		return t
	case string:
		val, _ := decimal.NewFromString(InterfaceToString(i))
		return val
	}
	return decimal.Zero
}

// ConvertInt64ToInt
func ConvertInt64ToInt(param int64) (result int) {
	strParam := strconv.FormatInt(param, 10)
	result, _ = strconv.Atoi(strParam)
	return
}

func ConvertProtoTimestampToMillis(seconds int64, nanos int32) int64 {
	return seconds*1000 + int64(nanos)/1_000_000
}
