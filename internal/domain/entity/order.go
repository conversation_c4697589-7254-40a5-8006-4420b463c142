package entity

import (
	"github.com/shopspring/decimal"
)

type Order struct {
	OrderID         string          `gorm:"column:order_id;primaryKey" json:"order_id"`
	ParentID        string          `gorm:"column:parent_id" json:"parent_id"`
	UID             string          `gorm:"column:uid" json:"uid"`
	UserID          string          `gorm:"column:user_id" json:"user_id"` // ✅ 新增
	Symbol          string          `gorm:"column:symbol" json:"symbol"`
	Side            int             `gorm:"column:side" json:"side"`
	Type            int             `gorm:"column:type" json:"type"` // ✅ 主訂單類型
	OrderType       int             `gorm:"column:order_type" json:"order_type"`
	TimeInForceType int             `gorm:"column:time_in_force_type" json:"time_in_force_type"`
	TriggerType     int             `gorm:"column:trigger_type" json:"trigger_type"`
	ReduceOnly      bool            `gorm:"column:reduce_only" json:"reduce_only"`
	WorkType        int             `gorm:"column:work_type" json:"work_type"`
	PosSide         int             `gorm:"column:pos_side" json:"pos_side"`
	Price           decimal.Decimal `gorm:"column:price" json:"price"`
	TriggerPrice    decimal.Decimal `gorm:"column:trigger_price" json:"trigger_price"`
	Amount          decimal.Decimal `gorm:"column:amount" json:"amount"`
	Volume          decimal.Decimal `gorm:"column:volume" json:"volume"`
	Leverage        int             `gorm:"column:leverage" json:"leverage"`

	UnfilledAmount decimal.Decimal `gorm:"column:unfilled_amount" json:"unfilled_amount"`
	UnfilledVolume decimal.Decimal `gorm:"column:unfilled_volume" json:"unfilled_volume"`
	FilledAmount   decimal.Decimal `gorm:"column:filled_amount" json:"filled_amount"`
	FilledVolume   decimal.Decimal `gorm:"column:filled_volume" json:"filled_volume"`
	AvgPrice       decimal.Decimal `gorm:"column:avg_price" json:"avg_price"`

	Status       int `gorm:"column:status" json:"status"`
	Source       int `gorm:"column:source" json:"source"`
	CancelSource int `gorm:"column:cancel_source" json:"cancel_source"` // ✅ 改為 cancel_source
	UserType     int `gorm:"column:user_type" json:"user_type"`
	UserLevel    int `gorm:"column:user_level" json:"user_level"`

	PositionMode int    `gorm:"column:position_mode" json:"position_mode"`
	MarginMode   int    `gorm:"column:margin_mode" json:"margin_mode"`
	AwardIDs     string `gorm:"column:-" json:"award_ids"`

	FeeRateMaker decimal.Decimal `gorm:"column:fee_rate_maker" json:"fee_rate_maker"`
	FeeRateTaker decimal.Decimal `gorm:"column:fee_rate_taker" json:"fee_rate_taker"`

	CreateTime    int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime    int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
	SubmittedTime int64 `gorm:"column:submitted_time" json:"submitted_time"`
}

func (Order) TableName() string {
	return "order"
}
