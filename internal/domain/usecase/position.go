package usecase

import (
	"context"

	"futures-asset/internal/domain/repository"
	"futures-asset/internal/utils"

	"github.com/shopspring/decimal"
)

type PositionUseCase interface {
	UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error)
	QueryUserPos(ctx context.Context, req *repository.UserPosParam) (repository.UserPosReply, error)
	PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error)
	PosTotal(ctx context.Context, contractCode string) decimal.Decimal
	UserHoldPos(ctx context.Context, req *repository.UserHoldPosReq) (repository.HoldPosReply, error)

	PlatPosList(ctx context.Context) (repository.PlatPosList, error)
	PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error)

	// 开仓操作
	OpenLongPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error
	OpenShortPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error
	OpenBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error

	// 平仓操作
	CloseLongPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error
	CloseShortPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error
	CloseBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error
}
