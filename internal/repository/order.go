package repository

import (
	"context"
	"errors"
	"time"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"go.uber.org/dig"
	"gorm.io/gorm"
)

type OrderRepositoryParam struct {
	dig.In

	DB *gorm.DB `name:"db"`
}

type OrderRepository struct {
	db *gorm.DB
}

func NewOrderRepository(param OrderRepositoryParam) repository.OrderRepository {
	return &OrderRepository{
		db: param.DB,
	}
}

// Create 建立委託單
func (repo *OrderRepository) Create(ctx context.Context, order *entity.Order) error {
	order.CreateTime = time.Now().UTC().Unix()
	err := repo.db.WithContext(ctx).Create(order).Error

	if err != nil {
		return repository.InsertRecordError{DBErr: err}
	}

	return nil
}

// UpdateOrder 更新order
func (repo *OrderRepository) UpdateOrder(ctx context.Context, order *entity.Order) error {
	err := repo.db.WithContext(ctx).Save(order).Error
	if err != nil {
		return repository.UpdateRecordError{DBErr: err}
	}

	return nil
}

func (repo *OrderRepository) Page(ctx context.Context, param repository.OrderPageParam) ([]entity.Order, int64, error) {
	sql := repo.db.WithContext(ctx)

	var data []entity.Order
	var count int64

	if param.UID != "" {
		sql = sql.Where("uid = ?", param.UID)
	}

	if param.OrderID != "" {
		sql = sql.Where("order_id = ?", param.OrderID)
	}

	if param.ContractType != 0 {
		sql = sql.Where("contract_type = ?", param.ContractType)
	}

	if param.Symbol != "" {
		sql = sql.Where("symbol = ?", param.Symbol)
	}

	if param.Side != 0 {
		sql = sql.Where("side = ?", param.Side)
	}

	if param.PositionSide != 0 {
		sql = sql.Where("position_side = ?", param.PositionSide)
	}

	if param.Status != 0 {
		sql = sql.Where("status = ?", param.Status)
	}

	if param.CloseType != 0 {
		sql = sql.Where("close_type = ?", param.CloseType)
	}

	if param.StartTime != 0 && param.EndTime != 0 {
		sql = sql.Where("create_time between ? and ?", param.StartTime, param.EndTime)
	}

	if param.MarginMode != 0 {
		sql = sql.Where("margin_mode = ?", param.MarginMode)
	}
	if param.NotCount != 1 {
		if err := sql.Model(&entity.Order{}).Count(&count).Error; err != nil {
			return []entity.Order{}, 0, err
		}
	}

	if param.PageIndex != 0 && param.PageSize != 0 {
		sql = sql.Offset((param.PageIndex - 1) * param.PageSize).Limit(param.PageSize)
	}

	err := sql.
		Order("create_time DESC").
		Find(&data).Error
	if err != nil {
		return []entity.Order{}, 0, err
	}

	return data, count, nil
}

func (repo *OrderRepository) GetByOrderId(ctx context.Context, orderId string) (*entity.Order, error) {
	resp := &entity.Order{}

	err := repo.db.WithContext(ctx).
		Where("`order_id` = ?", orderId).
		Take(resp).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, repository.ErrRecordNotFound
		}

		return nil, repository.QueryRecordError{DBErr: err}
	}

	return resp, nil
}
