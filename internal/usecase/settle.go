package usecase

import (
	"context"
	"errors"
	"fmt"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/typex"
	"futures-asset/internal/utils"
	"futures-asset/util"
	"github.com/shopspring/decimal"
	"strings"
	"time"

	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"

	"github.com/go-redsync/redsync/v4"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
	futuresPB "yt.com/backend/common.git/business/grpc/gen/futures/v1"
)

// SettleUseCase 结算用例实现
type SettleUseCase struct {
	cacheRepo  repository.CacheRepository
	priceRepo  repository.PriceRepository
	orderRepo  repository.OrderRepository
	tradeRepo  repository.TradeRepository
	positionUC usecase.PositionUseCase
	producerUC usecase.ProducerUseCase
	rs         *redsync.Redsync
}

// SettleUseCaseParam 结算用例参数
type SettleUseCaseParam struct {
	dig.In
	CacheRepo  repository.CacheRepository
	PriceRepo  repository.PriceRepository
	OrderRepo  repository.OrderRepository
	TradeRepo  repository.TradeRepository
	PositionUC usecase.PositionUseCase
	ProducerUC usecase.ProducerUseCase
	RS         *redsync.Redsync `name:"rs"`
}

// NewSettleUseCase 创建结算用例实例
func NewSettleUseCase(param SettleUseCaseParam) *SettleUseCase {
	return &SettleUseCase{
		cacheRepo:  param.CacheRepo,
		priceRepo:  param.PriceRepo,
		orderRepo:  param.OrderRepo,
		tradeRepo:  param.TradeRepo,
		positionUC: param.PositionUC,
		producerUC: param.ProducerUC,
		rs:         param.RS,
	}
}

// ProcessAccountSettle 处理账户结算业务
// 参考Trade.Trade()和Trade.Process()函数实现，但只处理单个账户参数
func (use *SettleUseCase) ProcessAccountSettle(accountSettle *futuresEnginePB.AccountSettleEngine) error {
	accountSettleParam := utils.AccountSettleParam{AccountSettle: accountSettle}
	uid := accountSettleParam.GetUID()
	base, quote := accountSettleParam.GetBaseQuote()

	if uid == "" || base == "" || quote == "" {
		return fmt.Errorf("invalid account settle data: uid=%s, base=%s, quote=%s", uid, base, quote)
	}

	// 锁定用户资产
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+uid, redsync.WithExpiry(30*time.Second))
	if err := userMutex.Lock(); err != nil {
		return fmt.Errorf("failed to lock user asset: err=%v, uid=%s", err, uid)
	}
	defer func() {
		unlockOk, unlockErr := userMutex.Unlock()
		if unlockErr != nil || !unlockOk {
			logrus.Errorf("failed to unlock user asset: ok=%v, err=%v, uid=%s", unlockOk, unlockErr, uid)
		}
	}()

	// 加载用户资产
	ctx := context.Background()
	contractCode := util.ContractCode(base, quote)
	userAsset, err := use.cacheRepo.Load(ctx, uid, contractCode, quote)
	if err != nil {
		return fmt.Errorf("failed to load user asset: err=%v, uid=%s", err, uid)
	}
	// 打印用户资产信息到日志
	logrus.WithFields(logrus.Fields{
		"tradeid":      accountSettleParam.GetTradeId(),
		"uid":          uid,
		"contractCode": contractCode,
		"PositionMode": userAsset.PositionMode.String(),
		"AssetMode":    userAsset.AssetMode.String(),
		"Balance":      userAsset.Balance,
		"LongPos":      userAsset.LongPos,
		"ShortPos":     userAsset.ShortPos,
		"BothPos":      userAsset.BothPos,
	}).Info("tp0803 user current asset")

	// 处理结算业务逻辑
	changeLog := make([]string, 0)
	err = use.processAccountSettlement(ctx, userAsset, accountSettleParam, &changeLog)
	if err != nil {
		return fmt.Errorf("process settlement error: err=%v, uid=%s", err, uid)
	}

	// 打印变更后用户资产信息到日志
	logrus.WithFields(logrus.Fields{
		"tradeid":      accountSettleParam.GetTradeId(),
		"orderid":      accountSettleParam.GetOrderID(),
		"uid":          uid,
		"contractCode": contractCode,
		"PositionMode": userAsset.PositionMode.String(),
		"AssetMode":    userAsset.AssetMode.String(),
		"Balance":      userAsset.Balance,
		"LongPos":      userAsset.LongPos,
		"ShortPos":     userAsset.ShortPos,
		"BothPos":      userAsset.BothPos,
		"changeLog":    changeLog,
	}).Info("tp0803 user asset change summary")

	// 保存落库更新后的资产
	err = use.saveUserAsset(ctx, userAsset, contractCode)
	if err != nil {
		return fmt.Errorf("save user asset error: err=%v, uid=%s", err, uid)
	}

	// 更新订单和交易记录
	err = use.upsertOrderAndTrade(ctx, userAsset, accountSettleParam, changeLog)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"accountSettleParam": accountSettleParam,
		}).Errorf("upsert order and trade error: err=%v, uid=%s", err, uid)
	}

	return nil
}

// processAccountSettlement 处理账户结算的核心业务逻辑
// 参考原来的Trade.Process()函数实现，但只处理单个账户的结算
func (use *SettleUseCase) processAccountSettlement(ctx context.Context, userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {

	if userAsset == nil {
		return fmt.Errorf("user asset is nil")
	}

	openclose := "close"
	if accountSettleParam.IsOpenPosition() {
		openclose = "open"
	}

	logrus.WithFields(logrus.Fields{
		"tradeid":       accountSettleParam.GetTradeId(),
		"orderid":       accountSettleParam.GetOrderID(),
		"uid":           userAsset.UID,
		"openclose":     openclose,
		"pos_side":      accountSettleParam.AccountSettle.Order.PosSide.String(),
		"buy_sell":      accountSettleParam.AccountSettle.Order.Side.String(),
		"price":         accountSettleParam.GetPrice(),
		"amount":        accountSettleParam.GetAmount(),
		"margin_mode":   accountSettleParam.AccountSettle.Order.MarginMode.String(),
		"position_mode": userAsset.PositionMode.String(),
	}).Info("tp0803 trade info")

	// 根据持仓模式处理结算
	if userAsset.PositionMode == futuresPB.PositionMode_POSITION_MODE_HEDGE {
		// 双向Hedge持仓结算逻辑
		err := use.settleSinglePosition(userAsset, accountSettleParam, changelog)
		if err != nil {
			return err
		}
	} else {
		// 单向Both持仓结算逻辑
		err := use.settleBothPositions(userAsset, accountSettleParam, changelog)
		if err != nil {
			return err
		}
	}

	return nil
}

func (use *SettleUseCase) settleSinglePosition(userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {

	side := accountSettleParam.GetSide()
	isOpen := accountSettleParam.IsOpenPosition()

	var err error
	if side == domain.Sell && isOpen {
		err = use.positionUC.OpenShortPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	} else if side == domain.Sell && !isOpen {
		err = use.positionUC.CloseLongPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	} else if side == domain.Buy && isOpen {
		err = use.positionUC.OpenLongPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	} else if side == domain.Buy && !isOpen {
		err = use.positionUC.CloseShortPos(userAsset, use.priceRepo, accountSettleParam, changelog)
	}

	return err
}

func (use *SettleUseCase) settleBothPositions(userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {

	// 体验金?
	// if isTrial {
	// 	// 体验金不支持单向持仓
	// 	err = fmt.Errorf("trial not support both pos")
	// 	return reply, domain.Code251026, err
	// }

	side := accountSettleParam.GetSide()
	isOpen := accountSettleParam.IsOpenPosition()
	amount := accountSettleParam.GetAmount()

	if side == domain.Sell {
		amount = amount.Neg() // 空仓置为负数?
	}

	if (side == domain.Buy && isOpen && userAsset.BothPos.Pos.Sign() >= 0) ||
		(side == domain.Sell && isOpen && userAsset.BothPos.Pos.Sign() <= 0) { // Brad说单向模式负数代表空仓
		// 方向一致直接开仓
		err := use.positionUC.OpenBothPos(userAsset, use.priceRepo, accountSettleParam, changelog)
		if err != nil {
			return err
		}
	} else {
		// price := accountSettleParam.GetPrice()
		// feeRate := accountSettleParam.GetFeeRate()
		// closeAmount := amount.Abs()
		// openAmount := decimal.Zero
		// closeFee := closeAmount.Mul(price).Mul(feeRate).Truncate(domain.CurrencyPrecision)
		// cUnfrozenMargin := accountSettleParam.GetUnfrozenMargin()

		// if userAsset.BothPos.Pos.Abs().LessThan(tradeAmount.Abs()) {
		// 	closeAmount = bothPos.Pos.Neg()
		// 	closeFee = trade.Fee.Mul(closeAmount.Abs().Div(req.Amount)).Abs().Truncate(domain.CurrencyPrecision)

		// 	cUnfrozenMargin = trade.UnfrozenMargin.Mul(closeAmount.Abs().Div(req.Amount)).Abs().Truncate(domain.CurrencyPrecision)
		// 	openAmount = bothPos.Pos.Add(tradeAmount)
		// }

		// 反向对冲平仓?
		err := use.positionUC.CloseBothPos(userAsset, use.priceRepo, accountSettleParam, changelog)
		if err != nil {
			return err
		}
		// Todo 再开仓
	}

	return nil
}

func (use *SettleUseCase) saveUserAsset(ctx context.Context, userAsset *repository.AssetSwap, contractCode string) error {
	if userAsset == nil {
		return fmt.Errorf("user asset is nil")
	}
	var data []*commonpb.KafkaFundingChange

	for currency, balance := range userAsset.Balance {
		data = append(data, &commonpb.KafkaFundingChange{
			Uid:        userAsset.UID,
			Currency:   currency,
			ChangeType: commonpb.FundingChangeType_FundingChangeType_Balance,
			Value:      balance.String(),
		})
	}
	for currency, frozen := range userAsset.Frozen {
		data = append(data, &commonpb.KafkaFundingChange{
			Uid:        userAsset.UID,
			Currency:   currency,
			ChangeType: commonpb.FundingChangeType_FundingChangeType_Frozen,
			Value:      frozen.String(),
		})
	}

	// 将资产余额 发送到kafka，供后续更新
	err := use.producerUC.SendFundingChange(data)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"uid":          userAsset.UID,
			"contractCode": contractCode,
			"PositionMode": userAsset.PositionMode.String(),
			"AssetMode":    userAsset.AssetMode.String(),
			"Balance":      userAsset.Balance,
			"Frozen":       userAsset.Frozen,
			"LongPos":      userAsset.LongPos,
			"ShortPos":     userAsset.ShortPos,
			"BothPos":      userAsset.BothPos,
			"error":        err,
		}).Error("SettleUseCase.saveUserAsset")
	}
	logrus.Infof("saveUserAsset: uid=%s, contractCode=%s", userAsset.UID, contractCode)

	logrus.Info("saveUserAsset: asset saved successfully (placeholder implementation)")
	return nil
}

func (use *SettleUseCase) getSettlementFeeRate(accountSettleParam utils.AccountSettleParam) (decimal.Decimal, error) {
	settleType := accountSettleParam.GetAccountSettleType()
	switch settleType {
	case "taker":
		return accountSettleParam.GetFeeRateTaker(), nil
	case "maker":
		return accountSettleParam.GetFeeRateMaker(), nil
	default:
		return decimal.Zero, fmt.Errorf("unsupported settle type: %s", settleType)
	}
}

func (use *SettleUseCase) upsertOrderAndTrade(ctx context.Context, userAsset *repository.AssetSwap, accountSettleParam utils.AccountSettleParam, changeLog []string) error {
	// 实现订单和交易记录的更新或插入逻辑
	settleOrder := accountSettleParam.AccountSettle.Order
	hasOldOrder := true

	orderPrice, _ := decimal.NewFromString(settleOrder.Price)
	triggerPrice, _ := decimal.NewFromString(settleOrder.TriggerPrice)
	orderAmount, _ := decimal.NewFromString(settleOrder.Amount)
	orderVolume, _ := decimal.NewFromString(settleOrder.Volume)
	unfilledAmount, _ := decimal.NewFromString(settleOrder.UnfilledAmount)
	unfilledVolume, _ := decimal.NewFromString(settleOrder.UnfilledVolume)
	filledAmount, _ := decimal.NewFromString(settleOrder.FilledAmount)
	filledVolume, _ := decimal.NewFromString(settleOrder.FilledVolume)
	avgPrice, _ := decimal.NewFromString(settleOrder.AvgPrice)
	feeRateMaker, _ := decimal.NewFromString(settleOrder.FeeRateMaker)
	feeRateTaker, _ := decimal.NewFromString(settleOrder.FeeRateTaker)

	oldOrder, err := use.orderRepo.GetByOrderId(ctx, settleOrder.OrderId)
	if err != nil && !errors.Is(err, repository.ErrRecordNotFound) {
		logrus.Errorf("failed to get order by orderId: %s, err: %v", settleOrder.OrderId, err)
		// 先直接返回只記錄LOG
		return nil
	}
	if err != nil {
		hasOldOrder = false
		oldOrder = &entity.Order{
			OrderID:         settleOrder.OrderId,
			ParentID:        settleOrder.ParentId,
			UID:             settleOrder.UserId,
			UserID:          settleOrder.UserId,
			Symbol:          fmt.Sprintf("%s-%s", settleOrder.Symbol.Base, settleOrder.Symbol.Quote),
			Side:            int(settleOrder.Side),
			Type:            int(settleOrder.Type),
			OrderType:       int(settleOrder.Type),
			TimeInForceType: int(settleOrder.TimeInForceType),
			TriggerType:     int(settleOrder.TriggerType),
			ReduceOnly:      settleOrder.ReduceOnly,
			WorkType:        int(settleOrder.WorkType),
			PosSide:         int(settleOrder.PosSide),
			Price:           orderPrice,
			TriggerPrice:    triggerPrice,
			Amount:          orderAmount,
			Volume:          orderVolume,
			Leverage:        int(settleOrder.Leverage),
			UnfilledAmount:  unfilledAmount,
			UnfilledVolume:  unfilledVolume,
			FilledAmount:    filledAmount,
			FilledVolume:    filledVolume,
			AvgPrice:        avgPrice,
			Status:          int(settleOrder.Status),
			Source:          int(settleOrder.Source),
			CancelSource:    int(settleOrder.CancelSource),
			UserType:        int(settleOrder.UserType),
			UserLevel:       int(settleOrder.UserLevel),
			PositionMode:    int(settleOrder.PositionMode),
			MarginMode:      int(settleOrder.MarginMode),
			AwardIDs:        strings.Join(settleOrder.AwardIds, ","),
			FeeRateMaker:    feeRateMaker,
			FeeRateTaker:    feeRateTaker,
			CreateTime:      typex.ConvertProtoTimestampToMillis(settleOrder.CreatedAt.Seconds, settleOrder.CreatedAt.Nanos),
			UpdateTime:      typex.ConvertProtoTimestampToMillis(settleOrder.UpdatedAt.Seconds, settleOrder.UpdatedAt.Nanos),
			SubmittedTime:   typex.ConvertProtoTimestampToMillis(settleOrder.SubmittedAt.Seconds, settleOrder.SubmittedAt.Nanos),
		}
	}
	if hasOldOrder {
		// 更新订单
		oldOrder.Price = orderPrice
		oldOrder.TriggerPrice = triggerPrice
		oldOrder.Amount = orderAmount
		oldOrder.Volume = orderVolume
		oldOrder.UnfilledAmount = unfilledAmount
		oldOrder.UnfilledVolume = unfilledVolume
		oldOrder.FilledAmount = filledAmount
		oldOrder.FilledVolume = filledVolume
		oldOrder.AvgPrice = avgPrice
		oldOrder.Status = int(settleOrder.Status)
		oldOrder.Source = int(settleOrder.Source)
		oldOrder.CancelSource = int(settleOrder.CancelSource)
		oldOrder.FeeRateMaker = feeRateMaker
		oldOrder.FeeRateTaker = feeRateTaker
		oldOrder.UpdateTime = typex.ConvertProtoTimestampToMillis(settleOrder.UpdatedAt.Seconds, settleOrder.UpdatedAt.Nanos)
		oldOrder.SubmittedTime = typex.ConvertProtoTimestampToMillis(settleOrder.SubmittedAt.Seconds, settleOrder.SubmittedAt.Nanos)
		// 更新订单的其他字段
		err = use.orderRepo.UpdateOrder(ctx, oldOrder)
	} else {
		logrus.Infof("upsert order: %s", settleOrder.OrderId)
		// 插入新订单
		err = use.orderRepo.Create(ctx, oldOrder)
	}
	if err != nil {
		logrus.Errorf("failed to update order: %s, err: %v", settleOrder.OrderId, err)
	}
	tradeVolume, _ := decimal.NewFromString(accountSettleParam.AccountSettle.Volume)

	var realizedPNL decimal.Decimal
	tradePrice := accountSettleParam.GetPrice()
	tradeAmount := accountSettleParam.GetAmount()
	feeRate, err := use.getSettlementFeeRate(accountSettleParam)

	// 扣手续费,余额变更
	fee := tradeAmount.Mul(tradePrice).Mul(feeRate)
	settleType := accountSettleParam.GetAccountSettleType()

	switch settleOrder.PosSide {
	case futuresEnginePB.PosSide_POS_SIDE_SHORT:
		realizedPNL = userAsset.ShortPos.CalcProfitReal(tradePrice, tradeAmount)
	case futuresEnginePB.PosSide_POS_SIDE_LONG:
		realizedPNL = userAsset.LongPos.CalcProfitReal(tradePrice, tradeAmount)
	case futuresEnginePB.PosSide_POS_SIDE_BOTH:
		realizedPNL = userAsset.BothPos.CalcProfitReal(tradePrice, tradeAmount)
	}
	trade := &entity.Trade{
		TradeID:         accountSettleParam.GetTradeId(),
		OrderID:         settleOrder.OrderId,
		UID:             settleOrder.UserId,
		Side:            int(settleOrder.Side),
		Price:           accountSettleParam.GetPrice(),
		Amount:          accountSettleParam.GetAmount(),
		Volume:          tradeVolume,
		Symbol:          fmt.Sprintf("%s-%s", settleOrder.Symbol.Base, settleOrder.Symbol.Quote),
		LiquidationType: int(accountSettleParam.GetLiquidationType()),
		Fee:             fee,
		RealizedPNL:     realizedPNL,
		MatchRole:       settleType,
		CreateTime: typex.ConvertProtoTimestampToMillis(accountSettleParam.AccountSettle.CreatedAt.Seconds,
			accountSettleParam.AccountSettle.CreatedAt.Nanos),
	}
	err = use.tradeRepo.Create(ctx, trade)
	if err != nil {
		logrus.Errorf("failed to create trade: %s, err: %v", trade.TradeID, err)
	}

	return nil
}
